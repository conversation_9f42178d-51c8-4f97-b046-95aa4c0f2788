// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_table_column_type_parent_record.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AppTableColumnTypeParentRecord _$AppTableColumnTypeParentRecordFromJson(
  Map<String, dynamic> json,
) => AppTableColumnTypeParentRecord(
  defaultValue: json['defaultValue'],
  columnDesc: json['columnDesc'] as String? ?? '',
  isSaveRequired: json['isSaveRequired'] as bool? ?? false,
  isSubmitRequired: json['isSubmitRequired'] as bool? ?? false,
  typeCode:
      $enumDecodeNullable(_$ColumnTypeEnumEnumMap, json['typeCode']) ??
      ColumnTypeEnum.parentRecord,
);

Map<String, dynamic> _$AppTableColumnTypeParentRecordToJson(
  AppTableColumnTypeParentRecord instance,
) => <String, dynamic>{
  'defaultValue': instance.defaultValue,
  'isSaveRequired': instance.isSaveRequired,
  'isSubmitRequired': instance.isSubmitRequired,
  'columnDesc': instance.columnDesc,
  'typeCode': _$ColumnTypeEnumEnumMap[instance.typeCode]!,
};

const _$ColumnTypeEnumEnumMap = {
  ColumnTypeEnum.text: 'text',
  ColumnTypeEnum.singleSelect: 'singleSelect',
  ColumnTypeEnum.multipleSelect: 'multipleSelect',
  ColumnTypeEnum.number: 'number',
  ColumnTypeEnum.date: 'date',
  ColumnTypeEnum.percentage: 'percentage',
  ColumnTypeEnum.checkbox: 'checkbox',
  ColumnTypeEnum.hyperlink: 'hyperlink',
  ColumnTypeEnum.parentRecord: 'parentRecord',
  ColumnTypeEnum.telephone: 'telephone',
  ColumnTypeEnum.currency: 'currency',
};
