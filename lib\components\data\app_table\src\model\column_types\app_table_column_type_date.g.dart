// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_table_column_type_date.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AppTableColumnTypeDate _$AppTableColumnTypeDateFromJson(
  Map<String, dynamic> json,
) =>
    AppTableColumnTypeDate(
        defaultValue: json['defaultValue'],
        columnDesc: json['columnDesc'] as String? ?? '',
        dateType: (json['dateType'] as num?)?.toInt() ?? 1,
        showWeek: json['showWeek'] as bool? ?? false,
        showTime: json['showTime'] as bool? ?? false,
        defaultValueType: (json['defaultValueType'] as num?)?.toInt() ?? 1,
      )
      ..isSaveRequired = json['isSaveRequired'] as bool
      ..isSubmitRequired = json['isSubmitRequired'] as bool
      ..typeCode = $enumDecode(_$ColumnTypeEnumEnumMap, json['typeCode']);

Map<String, dynamic> _$AppTableColumnTypeDateToJson(
  AppTableColumnTypeDate instance,
) => <String, dynamic>{
  'defaultValue': instance.defaultValue,
  'isSaveRequired': instance.isSaveRequired,
  'isSubmitRequired': instance.isSubmitRequired,
  'columnDesc': instance.columnDesc,
  'dateType': instance.dateType,
  'showWeek': instance.showWeek,
  'showTime': instance.showTime,
  'defaultValueType': instance.defaultValueType,
  'typeCode': _$ColumnTypeEnumEnumMap[instance.typeCode]!,
};

const _$ColumnTypeEnumEnumMap = {
  ColumnTypeEnum.text: 'text',
  ColumnTypeEnum.singleSelect: 'singleSelect',
  ColumnTypeEnum.multipleSelect: 'multipleSelect',
  ColumnTypeEnum.number: 'number',
  ColumnTypeEnum.date: 'date',
  ColumnTypeEnum.percentage: 'percentage',
  ColumnTypeEnum.checkbox: 'checkbox',
  ColumnTypeEnum.hyperlink: 'hyperlink',
  ColumnTypeEnum.parentRecord: 'parentRecord',
  ColumnTypeEnum.telephone: 'telephone',
  ColumnTypeEnum.currency: 'currency',
};
