// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_table_column_type_checkbox.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AppTableColumnTypeCheckbox _$AppTableColumnTypeCheckboxFromJson(
  Map<String, dynamic> json,
) => AppTableColumnTypeCheckbox(
  defaultValue: json['defaultValue'] ?? false,
  columnDesc: json['columnDesc'] as String? ?? '',
  isSaveRequired: json['isSaveRequired'] as bool? ?? false,
  isSubmitRequired: json['isSubmitRequired'] as bool? ?? false,
)..typeCode = $enumDecode(_$ColumnTypeEnumEnumMap, json['typeCode']);

Map<String, dynamic> _$AppTableColumnTypeCheckboxToJson(
  AppTableColumnTypeCheckbox instance,
) => <String, dynamic>{
  'defaultValue': instance.defaultValue,
  'isSaveRequired': instance.isSaveRequired,
  'isSubmitRequired': instance.isSubmitRequired,
  'columnDesc': instance.columnDesc,
  'typeCode': _$ColumnTypeEnumEnumMap[instance.typeCode]!,
};

const _$ColumnTypeEnumEnumMap = {
  ColumnTypeEnum.text: 'text',
  ColumnTypeEnum.singleSelect: 'singleSelect',
  ColumnTypeEnum.multipleSelect: 'multipleSelect',
  ColumnTypeEnum.number: 'number',
  ColumnTypeEnum.date: 'date',
  ColumnTypeEnum.percentage: 'percentage',
  ColumnTypeEnum.checkbox: 'checkbox',
  ColumnTypeEnum.hyperlink: 'hyperlink',
  ColumnTypeEnum.parentRecord: 'parentRecord',
  ColumnTypeEnum.telephone: 'telephone',
  ColumnTypeEnum.currency: 'currency',
};
