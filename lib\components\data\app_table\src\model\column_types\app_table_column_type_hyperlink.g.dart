// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_table_column_type_hyperlink.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AppTableColumnTypeHyperlink _$AppTableColumnTypeHyperlinkFromJson(
  Map<String, dynamic> json,
) => AppTableColumnTypeHyperlink(
  defaultValue: json['defaultValue'],
  columnDesc: json['columnDesc'] as String? ?? '',
  isSaveRequired: json['isSaveRequired'] as bool? ?? false,
  isSubmitRequired: json['isSubmitRequired'] as bool? ?? false,
  typeCode:
      $enumDecodeNullable(_$ColumnTypeEnumEnumMap, json['typeCode']) ??
      ColumnTypeEnum.hyperlink,
);

Map<String, dynamic> _$AppTableColumnTypeHyperlinkToJson(
  AppTableColumnTypeHyperlink instance,
) => <String, dynamic>{
  'defaultValue': instance.defaultValue,
  'isSaveRequired': instance.isSaveRequired,
  'isSubmitRequired': instance.isSubmitRequired,
  'columnDesc': instance.columnDesc,
  'typeCode': _$ColumnTypeEnumEnumMap[instance.typeCode]!,
};

const _$ColumnTypeEnumEnumMap = {
  ColumnTypeEnum.text: 'text',
  ColumnTypeEnum.singleSelect: 'singleSelect',
  ColumnTypeEnum.multipleSelect: 'multipleSelect',
  ColumnTypeEnum.number: 'number',
  ColumnTypeEnum.date: 'date',
  ColumnTypeEnum.percentage: 'percentage',
  ColumnTypeEnum.checkbox: 'checkbox',
  ColumnTypeEnum.hyperlink: 'hyperlink',
  ColumnTypeEnum.parentRecord: 'parentRecord',
  ColumnTypeEnum.telephone: 'telephone',
  ColumnTypeEnum.currency: 'currency',
};
