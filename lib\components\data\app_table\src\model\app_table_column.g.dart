// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_table_column.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AppTableColumn _$AppTableColumnFromJson(Map<String, dynamic> json) =>
    AppTableColumn(
      field: json['field'] as String,
      text: json['text'] as String,
      typeCode:
          $enumDecodeNullable(_$ColumnTypeEnumEnumMap, json['typeCode']) ??
          ColumnTypeEnum.text,
      type: AppTableColumn._typeFromJson(json['type'] as Map<String, dynamic>),
      width: (json['width'] as num?)?.toDouble() ?? 200,
      minWidth: (json['minWidth'] as num?)?.toDouble() ?? 100,
      showMore: json['showMore'] as bool? ?? true,
      sortable: json['sortable'] as bool? ?? false,
      resizable: json['resizable'] as bool? ?? false,
      frozen:
          $enumDecodeNullable(_$AppTableColumnFrozenEnumMap, json['frozen']) ??
          AppTableColumnFrozen.none,
    );

Map<String, dynamic> _$AppTableColumnToJson(AppTableColumn instance) =>
    <String, dynamic>{
      'field': instance.field,
      'text': instance.text,
      'typeCode': _$ColumnTypeEnumEnumMap[instance.typeCode]!,
      'type': AppTableColumn._typeToJson(instance.type),
      'width': instance.width,
      'minWidth': instance.minWidth,
      'showMore': instance.showMore,
      'sortable': instance.sortable,
      'resizable': instance.resizable,
      'frozen': _$AppTableColumnFrozenEnumMap[instance.frozen]!,
    };

const _$ColumnTypeEnumEnumMap = {
  ColumnTypeEnum.text: 'text',
  ColumnTypeEnum.singleSelect: 'singleSelect',
  ColumnTypeEnum.multipleSelect: 'multipleSelect',
  ColumnTypeEnum.number: 'number',
  ColumnTypeEnum.date: 'date',
  ColumnTypeEnum.percentage: 'percentage',
  ColumnTypeEnum.checkbox: 'checkbox',
  ColumnTypeEnum.hyperlink: 'hyperlink',
  ColumnTypeEnum.parentRecord: 'parentRecord',
  ColumnTypeEnum.telephone: 'telephone',
  ColumnTypeEnum.currency: 'currency',
};

const _$AppTableColumnFrozenEnumMap = {
  AppTableColumnFrozen.none: 'none',
  AppTableColumnFrozen.start: 'start',
};
