import 'dart:convert';
import 'dart:ui';

import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:octasync_client/components/data/app_table/src/add_column_content.dart';
import 'package:octasync_client/components/data/app_table/src/dashed_line.dart';
import 'package:octasync_client/components/data/app_table/src/enums/table_checked_enum.dart';
import 'package:octasync_client/components/data/app_table/src/model/app_table_column.dart';
import 'package:octasync_client/components/data/app_table/src/model/app_table_row_data.dart';
import 'package:octasync_client/components/data/app_table/src/shadow_box.dart';
import 'package:octasync_client/components/data/app_table/src/state/app_table_state_manage.dart';
import 'package:octasync_client/components/data/app_table/src/ui/cells/app_table_cell.dart';
import 'package:octasync_client/imports.dart';
import 'package:provider/provider.dart';
import 'package:linked_scroll_controller/linked_scroll_controller.dart';

class AppTable extends StatefulWidget {
  // /// 是否显示loading效果
  // final bool loading;

  // /// 显示列
  // final List<AppTableColumn> columns;

  // /// 显示行数据
  // final List<Map<String, dynamic>> rows;

  final TableCheckedEnum checkType;

  /// 行高
  final double rowHeight;

  /// 表格背景色
  final Color tableBgColor;

  /// 边框颜色
  final Color borderColor;

  /// 表格容器外边框颜色
  final Color tableOutsideBorderColor;

  /// 表头背景颜色
  final Color headerBgColor;

  /// 边框宽度
  final double borderWidth;

  /// 树结构连接线
  final Color joinLineColor;

  /// 序号列宽度
  final double indexColumnWidth;

  /// 表格外部容器的padding
  final double tableWrapperPadding;

  /// 空白行总数（尽量不要用，用 listviewPddingBottom 代替）
  final int emptyRowTotal;

  /// 表格底部留白
  final double listviewPddingBottom;

  /// 树结构支持最大层级
  final int? maxLevel;

  final EdgeInsetsGeometry? headerCellPadding;

  final EdgeInsetsGeometry? cellPadding;

  /// 序号单元格构建器
  final Widget Function(BuildContext context, int index)? indexCellBuilder;

  // // 添加树形单元格构建器
  // final Widget Function(
  //   BuildContext context,
  //   AppTableRowData rowData,
  //   String field,
  // )?
  // treeColumnBuilder;

  // 树形表格配置
  final double indentSize; // 每级缩进大小
  final double treeIconSize; // 展开/收起图标大小
  final IconData expandedIcon; // 展开状态的图标
  final IconData collapsedIcon; // 收起状态的图标
  final bool defaultExpandAll; // 是否默认展开所有节点

  /// 如果不支持树结构，那么该参数必填，默认为id————需要从中获取主键
  final String uniqueId;

  /// 树形表格配置————需要从中获取主键
  final TreeTableConfig? treeConfig;

  final bool showAddRowButton; // 是否显示添加行按钮（表格最底部的按钮行）
  final bool showAddColumnButton; //是否显示添加列按钮（表格最右侧的按钮列）

  final Color? hoverRowColor;

  final double frozenAreaMaxWidth; // 冻结区域最大宽度

  /// 单元格点击事件
  final void Function(BuildContext context, int rowIndex, int columnIndex, dynamic value)?
  onCellTap;

  // /// 添加列
  // /// [context] 上下文对象
  // /// [column] 新增列对象
  // /// [stateManager] 状态管理对象
  // /// [tableViewportWidth] 当前表格的可视区域宽度
  // final void Function(
  //   BuildContext context,
  //   AppTableColumn column,
  //   AppTableStateManage stateManager,
  //   double tableViewportWidth,
  // )?
  // onAddColumn;

  // /// 排序事件
  // final void Function(int columnIndex, bool ascending)? onSort;

  final void Function(AppTableStateManage stateManage)? onLoaded;

  final void Function()? onLoadMore;

  const AppTable({
    super.key,
    // this.loading = false,
    // required this.columns,
    // required this.rows,
    // this.treeColumnBuilder, // 新增参数
    this.checkType = TableCheckedEnum.none,
    this.indentSize = 24.0,
    this.treeIconSize = 24.0,
    this.expandedIcon = Icons.arrow_drop_down_rounded,
    this.collapsedIcon = Icons.arrow_right_rounded,
    this.rowHeight = 50,
    this.tableBgColor = const Color.fromARGB(10, 235, 224, 223),
    this.borderColor = Colors.blue,
    this.tableOutsideBorderColor = Colors.red,

    ///注意，headerBgColor 不能有透明度
    this.headerBgColor = const Color.fromARGB(255, 250, 248, 247),
    this.joinLineColor = Colors.blue,
    this.borderWidth = 1,
    this.indexColumnWidth = 50,
    this.tableWrapperPadding = 108,
    this.emptyRowTotal = 0,
    this.listviewPddingBottom = 150,
    this.maxLevel,
    this.headerCellPadding = const EdgeInsets.symmetric(horizontal: 4, vertical: 0),
    this.cellPadding = const EdgeInsets.symmetric(horizontal: 4, vertical: 0),
    this.defaultExpandAll = false,
    this.showAddRowButton = false,
    this.showAddColumnButton = false,
    this.hoverRowColor = const Color.fromARGB(50, 207, 206, 206),
    this.uniqueId = 'id',
    this.treeConfig,
    this.indexCellBuilder,
    this.onCellTap,
    this.frozenAreaMaxWidth = 1000,
    // this.onSort,
    // this.onAddColumn,
    this.onLoadMore,
    this.onLoaded,
  });

  @override
  State<AppTable> createState() => _AppTableState();
}

class _AppTableState extends State<AppTable> {
  // 是否为树结构
  bool get _isTree => widget.treeConfig != null;

  /// 行的唯一标识
  String get _rowKey => _isTree ? widget.treeConfig!.idField : widget.uniqueId;

  late AppTableStateManage _appTableStateManage;

  //table可视窗口宽度
  double _tableViewportWidth = -1;
  //table可视窗口高度
  double _tableViewportHeight = -1;

  double addColumnContentWidth = 400;

  /// 遗留问题：
  /// 1、数据行数较多（产生滚动垂直滚动条时），最后一行边框重叠，看上去有两个px————处理办法，在外部container添加底部padding（8px）；
  /// 2、因为表格布局是通过外部container的边框，配合所有单元格的right、bottom边框设置的，所以点击选中单元格时，设置单元格的所有边框颜色和宽度，看上去单元格上、左边框没有设置颜色一样；
  /// 3、无法同时显示x、y轴滚动条（y轴滚动条样式、交互和x轴不同，且x轴拖动到最右侧，y轴滚动条才能看到）；
  ///
  ///

  // 横向滚动条
  late ScrollController _horizontalScrollController;

  late LinkedScrollControllerGroup _controllers;
  // 纵向滚动条
  late ScrollController _verticalScrollController;
  // 冻结区域纵向滚动条
  late ScrollController _verticalScrollControllerFrozen;
  // 显示在右侧的纵向滚动条——listview如果有x滚动条，会导致不方便操作，所以需要该滚动条，绝对定位在右侧
  late ScrollController _verticalScrollControllerShadow;

  late ScrollController _verticalScrollControllerShadow2;

  //私有变量
  final double _listviewPddingBottom = 150;

  // 拖拽冻结列小块宽、高
  final double _frozenDragBlockWidth = 5;
  final double _frozenDragBlockHeight = 60;

  //表格索引列对应的图标大小
  double _indexColIconWidth = 20;

  // // late AppCustomTooltipController _addColumnTooltipController;
  // // 添加列弹框显示状态（true：显示；false：不显示）
  // bool addColumnTooltipVisiable = false;
  // // 设置 addColumnTooltipVisiable 的时间（因为添加按钮有多个，会导致显示逻辑有问题，所以添加中间变量——隔断非用户操作导致的显示隐藏）
  // DateTime? setVisiableTime;

  void _initEvn(bool isInit) {
    if (isInit) {
      _horizontalScrollController = ScrollController();
      _controllers = LinkedScrollControllerGroup();
      _verticalScrollController = _controllers.addAndGet();
      _verticalScrollControllerFrozen = _controllers.addAndGet();
      _verticalScrollControllerShadow = _controllers.addAndGet();
      _verticalScrollControllerShadow2 = _controllers.addAndGet();

      // _addColumnTooltipController = AppCustomTooltipController();
    }

    _initStateManager();

    if (isInit) {
      // 等待下一帧再添加监听
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _horizontalScrollController.addListener(_onHorizontalScroll);
        _verticalScrollControllerShadow.addListener(_onVerticalScroll);

        _verticalScrollControllerShadow.addListener(() {
          // 距离底部200像素时触发加载
          if (_verticalScrollControllerShadow.position.pixels >=
                  _verticalScrollControllerShadow.position.maxScrollExtent &&
              widget.onLoadMore != null) {
            widget.onLoadMore!();
          }
        });

        // final tablePosition = _getTableBounds(context)!.position;
        _appTableStateManage.setFrozenLinex();
      });
    }
  }

  @override
  void initState() {
    super.initState();

    _initEvn(true);

    _initOnLoaded();
  }

  // @override
  // void didUpdateWidget(covariant AppTable oldWidget) {
  //   super.didUpdateWidget(oldWidget);
  //   // columns 或 rows 发生变化时，重新初始化状态管理
  //   if (oldWidget.columns != widget.columns || oldWidget.rows != widget.rows) {
  //     _initEvn(false);
  //     // 需要 setState 以触发 Provider 更新
  //     setState(() {});
  //   }
  // }

  // @override
  // void didUpdateWidget(covariant AppTable oldWidget) {
  //   super.didUpdateWidget(oldWidget);

  //   if (oldWidget.loading != widget.loading) {
  //     // 这里可以添加一些逻辑来处理计数器更新
  //     print('2、Counter updated from ${oldWidget.loading} to ${widget.loading}');
  //   }
  // }

  // 滚动监听回调
  void _onHorizontalScroll() {
    // 直接使用 _appTableStateManage 而不是通过 Provider 获取
    double scrollOffset = _horizontalScrollController.offset;
    _appTableStateManage.onTableHorizontalScroll(scrollOffset);
  }

  void _onVerticalScroll() {
    _appTableStateManage.onTableVerticalScroll();
  }

  @override
  Widget build(BuildContext tableContext) {
    return ChangeNotifierProvider.value(
      value: _appTableStateManage,
      child: Builder(
        builder:
            (context) => LayoutBuilder(
              builder: (ctx, constraints) {
                var myProvider = context.read<AppTableStateManage>();
                return Selector<AppTableStateManage, ({double tableContentwidth})>(
                  selector:
                      (context, provider) => (
                        tableContentwidth: provider.tableContentWidth,
                        // isLoading: provider.loading,
                      ),
                  builder: (context, data, child) {
                    // table 内容宽度
                    var tbw = data.tableContentwidth + myProvider.tableWrapperPadding;

                    //table可视窗口宽度
                    _tableViewportWidth = constraints.maxWidth;
                    //table可视窗口高度
                    _tableViewportHeight = constraints.maxHeight;

                    // 设置表格相关信息
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      // 只在表格信息变化时才赋值
                      var info = _getTableBounds(context);

                      if (info != null) {
                        _appTableStateManage.setTableInfo(
                          _tableViewportWidth,
                          _tableViewportHeight,
                          info.position,
                          info.size,
                        );
                      }
                    });

                    //总共多少列
                    var colsTotal = myProvider.columns.length;

                    /// 整个表格容器的宽度（-2 为了防止表格所占宽度小于表格容器宽度显示x轴滚动条）
                    var containerWidth =
                        tbw > _tableViewportWidth - 2 ? tbw : _tableViewportWidth - 2;

                    return ScrollConfiguration(
                      // key: ValueKey(tabKey),
                      // behavior: ScrollConfiguration.of(
                      //   context,
                      // ).copyWith(scrollbars: true),
                      behavior: AppTableScrollBehavior(),
                      child: Container(
                        width: double.infinity,
                        decoration: BoxDecoration(
                          color: myProvider.tableBgColor,
                          border: Border.all(
                            color: widget.tableOutsideBorderColor,
                            width: myProvider.borderWidth,
                          ),
                        ),
                        child: Stack(
                          children: [
                            Container(
                              child: SizedBox(
                                width: containerWidth,
                                child: Column(
                                  children: [
                                    //表头占位
                                    SizedBox(height: colsTotal > 0 ? myProvider.headerHeight : 0),

                                    Expanded(
                                      child: Selector<
                                        AppTableStateManage,
                                        ({
                                          int colChange,
                                          int rowChange,
                                          // List<Map<String, dynamic>>
                                          // rows,
                                          double tableHeight,
                                        })
                                      >(
                                        selector:
                                            (context, provider) => (
                                              colChange: provider.columnsChanged,
                                              rowChange: provider.rowsChanged,
                                              // rows: provider.rows,
                                              tableHeight: provider.tableHeight,
                                            ),
                                        builder: (context, data, child) {
                                          return // !data.isLoading &&
                                          !widget.showAddRowButton &&
                                                  (myProvider.columns.isEmpty ||
                                                      myProvider.rows.isEmpty)
                                              ? Center(child: Text('暂无数据'))
                                              : Row(
                                                mainAxisSize: MainAxisSize.min,
                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                children: [
                                                  //左侧冻结列区域
                                                  Container(
                                                    width: myProvider.tableWidthOfFrozenArea,
                                                    height: myProvider.tableHeight,
                                                    child: ListView.builder(
                                                      padding: EdgeInsets.only(
                                                        bottom: _listviewPddingBottom,
                                                      ),

                                                      controller: _verticalScrollControllerFrozen,
                                                      itemCount:
                                                          myProvider.rows.length +
                                                          widget.emptyRowTotal +
                                                          (widget.showAddRowButton ? 1 : 0),
                                                      itemExtent: myProvider.rowHeight,
                                                      itemBuilder: (BuildContext ctxt, int index) {
                                                        var rowKey = 'frozen_column_row_';

                                                        if (widget.showAddRowButton &&
                                                            index == myProvider.rows.length) {
                                                          rowKey += 'add_row';
                                                        } else {
                                                          rowKey +=
                                                              '${myProvider.rows[index][_rowKey]}_${index}';
                                                        }

                                                        return Row(
                                                          key: ValueKey(rowKey),
                                                          children: _buildRow(
                                                            context,
                                                            tableContext,
                                                            index,
                                                            AppTableColumnFrozen.start,
                                                            _tableViewportWidth,
                                                            _tableViewportWidth,
                                                          ),
                                                        );
                                                      },
                                                    ),
                                                  ),
                                                  //右侧非冻结列区域
                                                  Expanded(
                                                    child: CupertinoScrollbar(
                                                      controller: _horizontalScrollController,
                                                      thumbVisibility: true,
                                                      child: SingleChildScrollView(
                                                        controller: _horizontalScrollController,
                                                        scrollDirection: Axis.horizontal,
                                                        child: Stack(
                                                          children: [
                                                            Container(
                                                              width:
                                                                  containerWidth -
                                                                  myProvider.tableWidthOfFrozenArea,
                                                              child: ListView.builder(
                                                                padding: EdgeInsets.only(
                                                                  bottom: _listviewPddingBottom,
                                                                ),
                                                                controller:
                                                                    _verticalScrollController,
                                                                itemCount:
                                                                    myProvider.rows.length +
                                                                    widget.emptyRowTotal +
                                                                    (widget.showAddRowButton
                                                                        ? 1
                                                                        : 0),
                                                                itemExtent: myProvider.rowHeight,
                                                                // cacheExtent: 2000,
                                                                itemBuilder: (
                                                                  BuildContext ctxt,
                                                                  int index,
                                                                ) {
                                                                  var rowKey =
                                                                      'not_frozen_column_row_';

                                                                  if (widget.showAddRowButton &&
                                                                      index ==
                                                                          myProvider.rows.length) {
                                                                    rowKey += 'add_row';
                                                                  } else {
                                                                    rowKey +=
                                                                        '${myProvider.rows[index][_rowKey]}_${index}';
                                                                  }

                                                                  return Row(
                                                                    key: ValueKey(rowKey),
                                                                    children: _buildRow(
                                                                      context,
                                                                      tableContext,
                                                                      index,
                                                                      AppTableColumnFrozen.none,
                                                                      _tableViewportWidth,
                                                                      _tableViewportWidth,
                                                                    ),
                                                                  );
                                                                },
                                                              ),
                                                            ),
                                                          ],
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                ],
                                              );
                                        },
                                      ),
                                    ),
                                  ],
                                ), // 表头
                              ),
                            ),

                            // 表格Y轴滚动条
                            Selector<
                              AppTableStateManage,
                              ({int rowChange, List<Map<String, dynamic>> rows, double tableHeight})
                            >(
                              selector:
                                  (context, provider) => (
                                    rowChange: provider.rowsChanged,
                                    rows: provider.rows,
                                    tableHeight: provider.tableHeight,
                                  ),
                              builder: (context, data, child) {
                                /// 修正场景：
                                /// 滚动位置（当滚动条处于最底部位置时，点击折叠节点时，滚动条仍处于底部，导致底部出现空白）;
                                WidgetsBinding.instance.addPostFrameCallback((_) {
                                  double maxScroll = 0;
                                  if (_verticalScrollControllerShadow.hasClients) {
                                    maxScroll =
                                        _verticalScrollControllerShadow.position.maxScrollExtent;
                                    if (_verticalScrollControllerShadow.offset > maxScroll) {
                                      _verticalScrollControllerShadow.jumpTo(maxScroll);
                                    }
                                  }
                                });

                                return Positioned(
                                  top: widget.rowHeight,
                                  right: 0,
                                  height: constraints.maxHeight - myProvider.headerHeight,
                                  child: CupertinoScrollbar(
                                    controller: _verticalScrollControllerShadow,
                                    thumbVisibility: true, // 关键：让滚动条始终可见
                                    child: Container(
                                      height: data.tableHeight,
                                      width: 10,
                                      child: ListView(
                                        padding: EdgeInsets.only(bottom: _listviewPddingBottom),
                                        controller: _verticalScrollControllerShadow,
                                        children: [
                                          for (
                                            int i = 0;
                                            i <
                                                data.rows.length +
                                                    widget.emptyRowTotal +
                                                    (widget.showAddRowButton ? 1 : 0);
                                            i++
                                          )
                                            SizedBox(height: myProvider.rowHeight),
                                        ],
                                      ),
                                    ),
                                  ),
                                );
                              },
                            ),

                            /// 拖拽行——提示线
                            Selector<
                              AppTableStateManage,
                              ({double? siblingNodeLineY, double siblingLineIndent})
                            >(
                              selector:
                                  (context, provider) => (
                                    siblingNodeLineY: provider.siblingNodeLineY,
                                    siblingLineIndent: provider.siblingLineIndent,
                                  ),
                              builder: (context, data, child) {
                                var tempY = data.siblingNodeLineY;
                                var tempIndent = data.siblingLineIndent;
                                if (tempY != null) {
                                  return Positioned(
                                    top: tempY - 1,
                                    left: tempIndent,
                                    child: Container(
                                      height: 3,
                                      width: myProvider.tableContentWidth - tempIndent,
                                      color: Colors.red,
                                    ),
                                  );
                                }
                                return const SizedBox.shrink();
                              },
                            ),

                            /// 拖拽行——提示块
                            Selector<
                              AppTableStateManage,
                              ({double? startRowY, double? offsetRowY})
                            >(
                              selector:
                                  (context, provider) => (
                                    startRowY: provider.startRowY,
                                    offsetRowY: provider.offsetRowY,
                                  ),
                              builder: (context, data, child) {
                                double? top;

                                if (data.startRowY != null && data.offsetRowY != null) {
                                  top = data.startRowY! - data.offsetRowY!;
                                  //拖拽行提示块向上不能超过表头
                                  if (top < myProvider.headerHeight) {
                                    top = myProvider.headerHeight;
                                  }
                                }

                                return top != null
                                    ? Positioned(
                                      top: top,
                                      left: 0,
                                      // IgnorePointer 鼠标点击后，按住shift+滚动实现左右滚动，不加此组件无法滚动
                                      child: IgnorePointer(
                                        child: Container(
                                          height: widget.rowHeight,
                                          width: myProvider.tableContentWidth,
                                          color: myProvider.selectedColumnColor!.withValues(
                                            alpha: 0.55,
                                          ),
                                        ),
                                      ),
                                    )
                                    : SizedBox.shrink();
                              },
                            ),

                            /// 拖拽行——父节点提示“框选框”
                            Selector<AppTableStateManage, ({double? parentNodeY})>(
                              selector: (context, provider) => (parentNodeY: provider.parentNodeY),
                              builder: (context, data, child) {
                                var tempY = data.parentNodeY;

                                return tempY != null
                                    ? Positioned(
                                      top: tempY,
                                      left: 0,
                                      child: Container(
                                        decoration: BoxDecoration(
                                          border: Border.all(color: Colors.red, width: 1),
                                        ),
                                        height: widget.rowHeight,
                                        width: myProvider.tableContentWidth,
                                      ),
                                    )
                                    : SizedBox.shrink();
                              },
                            ),

                            /// 拖拽行——小手（按钮）————真正的拖拽行排序按钮
                            /// 因为表格有虚拟滚动，所以如果拖拽行放到表格里面，在拖拽行滚动时，会导致拖拽事件 onVerticalDragEnd 被销毁，
                            /// 从而无法执行正常逻辑。所以表格行里面放置的拖拽按钮只是用于显示，正真拖拽按钮在此；
                            Selector<
                              AppTableStateManage,
                              ({int? hoverRowIdx, int verticalScrollFlag})
                            >(
                              selector:
                                  (context, provider) => (
                                    hoverRowIdx: provider.hoverRowIdx,
                                    verticalScrollFlag: provider.verticalScrollFlag,
                                  ),
                              builder: (context, data, child) {
                                var hoverRowIdx = data.hoverRowIdx;

                                double top = 0;
                                if (hoverRowIdx != null) {
                                  top =
                                      myProvider.headerHeight +
                                      (data.hoverRowIdx! * myProvider.rowHeight) -
                                      _verticalScrollController.offset;
                                }

                                // //// 暂时写死
                                // double left = 7;
                                // //如果有选择
                                // var isSelect =
                                //     widget.checkType != TableCheckedEnum.none;
                                // //如果不包含单选或多选
                                // if (!isSelect) {
                                //   left = 19;
                                // }

                                return hoverRowIdx != null
                                    ? Positioned(
                                      top: top,
                                      // left: left,
                                      child: Container(
                                        width: myProvider.indexColumnWidth,
                                        height: myProvider.headerHeight,
                                        child: Center(
                                          child: Row(
                                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                            children: [
                                              //使用 SizedBox 撑起高度
                                              // SizedBox(
                                              //   height: myProvider.headerHeight,
                                              // ),
                                              MouseRegion(
                                                cursor: SystemMouseCursors.move, //.resizeUpDown,
                                                onHover: (event) {
                                                  myProvider.handleHoverRow(hoverRowIdx);
                                                },
                                                onExit: (event) {
                                                  // myProvider.handleHoverRow(null);
                                                },
                                                child: GestureDetector(
                                                  behavior: HitTestBehavior.opaque,

                                                  onVerticalDragStart: (details) {
                                                    final tableBounds =
                                                        _getTableBounds(tableContext)!;
                                                    myProvider.rowDragStart(
                                                      tableBounds.position,
                                                      details,
                                                      hoverRowIdx,
                                                      tableContext,
                                                    );
                                                  },
                                                  onVerticalDragUpdate: (details) {
                                                    final tableBounds =
                                                        _getTableBounds(tableContext)!;

                                                    myProvider.rowDragUpdate(
                                                      context,
                                                      tableBounds.position,
                                                      tableBounds.size,
                                                      details,
                                                      hoverRowIdx,
                                                    );
                                                  },
                                                  onVerticalDragEnd: (details) {
                                                    final tableBounds =
                                                        _getTableBounds(tableContext)!;
                                                    myProvider.rowDragEnd(tableBounds.position);
                                                  },

                                                  /// 上下拖拽行组件
                                                  child: OverlayRowWidget(
                                                    listViewController: _verticalScrollController,
                                                    child: Icon(
                                                      size: _indexColIconWidth,
                                                      Icons.drag_indicator,
                                                      color: Colors.yellow,
                                                    ),
                                                  ),
                                                ),
                                              ),

                                              // 模拟占位，为了使 “上下拖拽行” 按钮能和底部按钮对齐；
                                              //对应——单选（多选）icon
                                              widget.checkType == TableCheckedEnum.multiple ||
                                                      widget.checkType == TableCheckedEnum.single
                                                  ? GestureDetector(
                                                    behavior: HitTestBehavior.translucent,
                                                    child: SizedBox(width: _indexColIconWidth),
                                                  )
                                                  : const SizedBox.shrink(),

                                              //对应——最大化icon
                                              GestureDetector(
                                                behavior: HitTestBehavior.translucent,
                                                child: SizedBox(width: _indexColIconWidth),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    )
                                    : SizedBox.shrink();
                              },
                            ),

                            // // 表头分为两部分——非冻结部分（x轴滚动条滚动影响的表头）
                            _buildHeaderSelector(
                              AppTableColumnFrozen.none,
                              _tableViewportWidth,
                              tableContext,
                            ),

                            /// 拖拽改变列宽的小组件（非冻结列）
                            ..._buildHeaderResize(context, tableContext, AppTableColumnFrozen.none),

                            // // 表头分为两部分——冻结部分（x轴滚动条滚动不影响的表头）
                            _buildHeaderSelector(
                              AppTableColumnFrozen.start,
                              _tableViewportWidth,
                              tableContext,
                            ),

                            // // 拖拽改变列宽提示线
                            Selector<
                              AppTableStateManage,
                              ({int? resizingColumnIndex, double? startX, double? offsetX})
                            >(
                              selector:
                                  (context, provider) => (
                                    resizingColumnIndex: provider.resizingColumnIndex,
                                    startX: provider.startX,
                                    offsetX: provider.offsetX,
                                  ),
                              builder: (context, data, child) {
                                if (data.resizingColumnIndex != null && data.offsetX != null) {
                                  return Positioned(
                                    left: data.startX! - data.offsetX!,
                                    top: 0,
                                    bottom: 0,
                                    child: Container(
                                      width: myProvider.dragSplitlineWidth,
                                      color: myProvider.dragSplitlineColor,
                                    ),
                                  );
                                }
                                return const SizedBox.shrink();
                              },
                            ),

                            // //拖拽列提示块
                            Selector<
                              AppTableStateManage,
                              ({
                                List<int> seledColumnIndexs,
                                double? dxOfDragColumnBlock,
                                Offset? endGlobalPosition,
                                int horizontalScrollFlag,
                                double viewportHeight,
                              })
                            >(
                              selector:
                                  (context, provider) => (
                                    seledColumnIndexs: provider.selectedColumnIndexs,
                                    dxOfDragColumnBlock: provider.dxOfDragColumnBlock,
                                    endGlobalPosition: provider.endGlobalPosition,
                                    horizontalScrollFlag: provider.horizontalScrollFlag,
                                    viewportHeight: provider.viewportHeight,
                                  ),
                              builder: (context, data, child) {
                                if (data.seledColumnIndexs.isNotEmpty &&
                                    data.dxOfDragColumnBlock != null &&
                                    data.endGlobalPosition != null) {
                                  //选择列的总宽度
                                  var blockWidth = myProvider.selectedColumnWidth;
                                  //表格信息
                                  final tablePosition = _getTableBounds(tableContext)!.position;
                                  //提示框定位left属性
                                  double blockLeft =
                                      data.endGlobalPosition!.dx -
                                      data.dxOfDragColumnBlock! -
                                      tablePosition.dx;
                                  //每一列的分割线的位置
                                  var lines = myProvider
                                      .globalPositionsOfColumns(context, tablePosition)
                                      .map((c) => c - tablePosition.dx);
                                  //纠正——提示框定位left属性
                                  if (blockLeft < lines.first) {
                                    blockLeft = lines.first;
                                  } else if (blockLeft > lines.last - blockWidth!) {
                                    blockLeft = lines.last - blockWidth;
                                  }

                                  return Positioned(
                                    left: blockLeft,
                                    top: 0,
                                    // IgnorePointer 鼠标点击后，按住shift+滚动实现左右滚动，不加此组件无法滚动
                                    child: IgnorePointer(
                                      child: Container(
                                        width: blockWidth,
                                        height: data.viewportHeight,
                                        color: myProvider.selectedColumnColor!.withValues(
                                          alpha: 0.55,
                                        ),
                                      ),
                                    ),
                                  );
                                }
                                return const SizedBox.shrink();
                              },
                            ),

                            /// 冻结列区域和非冻结列区域分割线
                            /// 冻结列提示线（固定）——用于拖拽改变冻结列数量
                            /// 冻结列拖拽区域（主要用于存放和表格高度（表头+body（不包含空白区域））相同的组件）
                            Selector<
                              AppTableStateManage,
                              ({
                                int rowChange,
                                List<Map<String, dynamic>> rows,
                                double tableHeight,
                                int columnsChanged,
                                int horizontalScrollFlag,
                                // double? frozenLineX,
                                bool isDragStarted,
                              })
                            >(
                              selector:
                                  (context, provider) => (
                                    rowChange: provider.rowsChanged,
                                    rows: provider.rows,
                                    tableHeight: provider.tableHeight,
                                    columnsChanged: provider.columnsChanged,
                                    horizontalScrollFlag: provider.horizontalScrollFlag,
                                    isDragStarted: provider.isDragStarted,
                                  ),
                              builder: (context, data, child) {
                                var v_ctrl = myProvider.horizontalScrollController;

                                return Positioned(
                                  top:
                                      widget.showAddRowButton
                                          ? 0
                                          : myProvider.headerHeight, //widget.rowHeight,
                                  left: myProvider.frozenLineXDefault! + 2,
                                  child: Container(
                                    // color: Colors.red,
                                    height: data.tableHeight,
                                    // color: Colors.yellow,
                                    width: myProvider.frozenLineWidth, // 给阴影留空间，防止被裁剪
                                    child: ListView(
                                      controller: _verticalScrollControllerShadow2,
                                      children: [
                                        for (
                                          int i = 0;
                                          i <
                                              data.rows.length +
                                                  widget.emptyRowTotal +
                                                  (widget.showAddRowButton ? 1 : 0);
                                          i++
                                        )
                                          Stack(
                                            clipBehavior: Clip.none, // 允许阴影溢出
                                            children: [
                                              MouseRegion(
                                                cursor: SystemMouseCursors.move,
                                                onHover: (event) {
                                                  // print('onHover');
                                                  myProvider.setFrozenLineHoverStart(
                                                    event.position,
                                                    event.localPosition,
                                                  );
                                                },
                                                onExit: (event) {
                                                  if (data.isDragStarted) {
                                                    return;
                                                  }
                                                  myProvider.setFrozenLineHoverStart(null, null);
                                                  // print('onExit');
                                                },
                                                child: GestureDetector(
                                                  onHorizontalDragStart: (details) {
                                                    // print(
                                                    //   'onHorizontalDragStart',
                                                    // );
                                                    final tableBounds =
                                                        _getTableBounds(tableContext)!;

                                                    myProvider.frozenLineDragStart(
                                                      tableBounds.position,
                                                      details,
                                                      tableContext,
                                                    );
                                                  },
                                                  onHorizontalDragUpdate: (details) {
                                                    final tableBounds =
                                                        _getTableBounds(tableContext)!;

                                                    myProvider.frozenDragUpdate(
                                                      context,
                                                      tableBounds.position,
                                                      tableBounds.size,
                                                      details,
                                                    );
                                                  },
                                                  onHorizontalDragEnd: (details) {
                                                    myProvider.frozenLineDragEnd();
                                                  },
                                                  behavior: HitTestBehavior.opaque,
                                                  child: Container(
                                                    width: 1, // 竖线宽度
                                                    margin: // 增加触发区域
                                                        EdgeInsets.symmetric(horizontal: 3),
                                                    decoration: BoxDecoration(
                                                      boxShadow: [
                                                        BoxShadow(
                                                          color: Colors.red, // 阴影颜色
                                                          offset: Offset(3, 0), // 只向右偏移
                                                          blurRadius:
                                                              v_ctrl.hasClients && v_ctrl.offset > 0
                                                                  ? myProvider.boxShadowWidth
                                                                  : 0, // 模糊半径
                                                          spreadRadius: 0, // 扩散半径
                                                        ),
                                                      ],
                                                    ),
                                                    height: myProvider.rowHeight,

                                                    child: SizedBox(
                                                      height: myProvider.rowHeight,
                                                      // child: Text('${i}'),
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                      ],
                                    ),
                                  ),
                                );
                              },
                            ),

                            /// 拖拽改变列宽的小组件（冻结列）
                            ..._buildHeaderResize(
                              context,
                              tableContext,
                              AppTableColumnFrozen.start,
                            ),

                            /// 冻结列提示线（拖拽提示线——虚线）————只用于显示，不响应事件
                            Selector<
                              AppTableStateManage,
                              ({Offset? frozenLineHoverStart, double viewportHeight})
                            >(
                              selector:
                                  (context, provider) => (
                                    frozenLineHoverStart: provider.frozenLineHoverStart,
                                    viewportHeight: provider.viewportHeight,
                                  ),
                              builder: (context, data, child) {
                                if (colsTotal == 0) {
                                  return const SizedBox.shrink();
                                }

                                var temp = data.frozenLineHoverStart;
                                final tablePosition = _getTableBounds(tableContext)!.position;

                                if (temp != null) {
                                  var leftTemp = temp.dx - tablePosition.dx + 5;

                                  leftTemp = _setLinesSide(context, tablePosition, leftTemp, 0);

                                  return Positioned(
                                    top: 0,
                                    // bottom: 0,
                                    left: leftTemp,
                                    child: IgnorePointer(
                                      child: DashedLine(
                                        width: 2,
                                        height: data.viewportHeight,
                                        dashWidth: 4,
                                        dashSpace: 5,
                                        color: Colors.red,
                                        direction: Axis.vertical,
                                      ),
                                    ),
                                  );
                                }

                                return const SizedBox.shrink();
                              },
                            ),

                            /// 拖拽冻结列的小方块————只用于显示，不响应事件
                            Selector<AppTableStateManage, ({Offset? frozenLineHoverStart})>(
                              selector:
                                  (context, provider) => (
                                    frozenLineHoverStart: provider.frozenLineHoverStart,
                                  ),
                              builder: (context, data, child) {
                                if (colsTotal == 0) {
                                  return const SizedBox.shrink();
                                }

                                var temp = data.frozenLineHoverStart;

                                if (temp != null) {
                                  final tablePosition = _getTableBounds(tableContext)!.position;

                                  var leftTemp =
                                      temp.dx -
                                      tablePosition.dx -
                                      (_frozenDragBlockWidth / 2).floor() +
                                      5;

                                  var half = (_frozenDragBlockWidth / 2).floor();

                                  leftTemp = _setLinesSide(context, tablePosition, leftTemp, half);

                                  return Positioned(
                                    top: temp.dy - tablePosition.dy - (_frozenDragBlockHeight / 2),
                                    left: leftTemp,
                                    child: IgnorePointer(
                                      child: Container(
                                        width: _frozenDragBlockWidth,
                                        height: _frozenDragBlockHeight,
                                        color: const Color.fromARGB(255, 235, 2, 177),
                                      ),
                                    ),
                                  );
                                }

                                return const SizedBox.shrink();
                              },
                            ),

                            // //匹配插入位置提示线（拖拽列排序，如果匹配到需要插入的位置，则显示提示线）
                            // 1：托列列插入匹配位置；2：拖拽冻结列匹配位置
                            Selector<
                              AppTableStateManage,
                              ({int insertIdx, double insertX, double viewportHeight})
                            >(
                              selector:
                                  (context, provider) => (
                                    insertIdx: provider.insertIdx,
                                    insertX: provider.insertX,
                                    viewportHeight: provider.viewportHeight,
                                  ),
                              builder: (context, data, child) {
                                if (data.insertIdx > -1) {
                                  return Positioned(
                                    left: data.insertX,
                                    top: 0,
                                    child: IgnorePointer(
                                      child: Container(
                                        width: myProvider.insertSplitlineWidth,
                                        height: data.viewportHeight,
                                        color: myProvider.insertSplitlineColor,
                                      ),
                                    ),
                                  );
                                }
                                return const SizedBox.shrink();
                              },
                            ),

                            // /// loading 效果遮罩
                            Selector<AppTableStateManage, ({bool isLoading})>(
                              selector: (context, provider) => (isLoading: provider.isLoading),
                              builder: (context, data, child) {
                                return data.isLoading
                                    ? Positioned(
                                      top: 0,
                                      right: 0,
                                      bottom: 0,
                                      left: 0,
                                      child: Container(
                                        color: const Color.fromRGBO(238, 238, 238, 0.7),
                                        child: Row(
                                          mainAxisAlignment: MainAxisAlignment.center,
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            CircularProgressIndicator(),
                                            SizedBox(width: 10),
                                            Text('加载中...'),
                                          ],
                                        ),
                                      ),
                                    )
                                    : const SizedBox.shrink();
                              },
                            ),

                            // /// loadingMore 效果（底部显示）
                            Selector<AppTableStateManage, ({bool loadingMore, bool hasMore})>(
                              selector:
                                  (context, provider) => (
                                    loadingMore: provider.loadingMore,
                                    hasMore: provider.hasMore,
                                  ),
                              builder: (context, data, child) {
                                return data.loadingMore
                                    ? Positioned(
                                      right: 0,
                                      bottom: 0,
                                      left: 0,
                                      child: Container(
                                        height: 30,
                                        color: const Color.fromRGBO(238, 238, 238, 0.7),
                                        child: Row(
                                          mainAxisAlignment: MainAxisAlignment.center,
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            SizedBox(
                                              width: 14,
                                              height: 14,
                                              child: CircularProgressIndicator(),
                                            ),

                                            SizedBox(width: 10),
                                            Text('加载更多...'),
                                          ],
                                        ),
                                      ),
                                    )
                                    : const SizedBox.shrink();
                              },
                            ),

                            // /// 测试信息展示区域（用于开发测试）

                            // Selector<
                            //   AppTableStateManage,
                            //   ({
                            //     Offset? startGlobalPosition,
                            //     Offset? endGlobalPosition,
                            //     bool loading,
                            //   })
                            // >(
                            //   selector:
                            //       (context, provider) => (
                            //         startGlobalPosition:
                            //             provider.startGlobalPosition,
                            //         endGlobalPosition:
                            //             provider.endGlobalPosition,
                            //         loading: provider.isLoading,
                            //       ),
                            //   builder: (context, data, child) {
                            //     return Positioned(
                            //       bottom: 0,
                            //       right: 0,
                            //       child: Column(
                            //         children: [
                            //           Container(child: Text('测试信息提示区域：')),
                            //           Container(
                            //             child: Text(data.loading.toString()),
                            //           ),
                            //           Container(
                            //             child: Text(
                            //               data.startGlobalPosition.toString(),
                            //             ),
                            //           ),
                            //           Container(
                            //             child: Text(
                            //               data.endGlobalPosition.toString(),
                            //             ),
                            //           ),
                            //         ],
                            //       ),
                            //     );
                            //   },
                            // ),

                            /// 测试按钮
                            // Positioned(
                            //   bottom: 0,
                            //   right: 0,
                            //   child: Row(
                            //     children: [
                            //       TextButton(
                            //         onPressed: () {
                            //           var nodeId = '2'; //1：技术部；2：前端组

                            //           var allRows =
                            //               _appTableStateManage.allRows;
                            //           var firstOfAll =
                            //               allRows
                            //                   .firstWhereOrNull(
                            //                     (c) =>
                            //                         c.data['nodeId'] == nodeId,
                            //                   )
                            //                   ?.data;

                            //           var flattenedList =
                            //               _appTableStateManage.rows;
                            //           var firstOfFlattened = flattenedList
                            //               .firstWhereOrNull(
                            //                 (c) => c['nodeId'] == nodeId,
                            //               );

                            //           print(
                            //             'object-${allRows.length}--${firstOfAll}',
                            //           );
                            //           print(
                            //             'object-${flattenedList.length}--${firstOfFlattened}',
                            //           );

                            //           var cols = _appTableStateManage.columns;

                            //           print('${cols}');
                            //         },
                            //         child: Text('获取数据'),
                            //       ),
                            //     ],
                            //   ),
                            // ),
                          ],
                        ),
                      ),
                    );
                  },
                );
              },
            ),
      ),
    );
  }

  Widget _buildHeaderSelector(
    AppTableColumnFrozen frozen,
    double tableViewportWidth,
    BuildContext tableContext,
  ) {
    return Selector<
      AppTableStateManage,
      ({List<AppTableColumn> cols, int columnsChanged, int horizontalScrollFlag})
    >(
      selector:
          (context, provider) => (
            cols: provider.columns,
            columnsChanged: provider.columnsChanged,
            horizontalScrollFlag: provider.horizontalScrollFlag,
          ),
      builder: (context, data, child) {
        if (data.cols.isEmpty) {
          return const SizedBox.shrink();
        }
        final myProvider = context.read<AppTableStateManage>();
        double offset =
            myProvider.horizontalScrollController.hasClients
                ? myProvider.horizontalScrollController.offset
                : 0;

        return Positioned(
          top: 0,
          left: frozen == AppTableColumnFrozen.none ? 0 - offset : 0,
          child: Row(
            children: _buildHeader(context, tableContext, data.cols, tableViewportWidth, frozen),
          ),
        );
      },
    );
  }

  /// 构建表格列头
  /// [context] 默认整体的 BuildContext
  /// [tableContext] 表格的 BuildContext
  /// [cols] 表格列集合
  /// [tableViewportWidth] 表格可视窗口宽度
  /// [frozen] 是否为冻结区域（表格由两部分组成，左侧冻结区域、右侧非冻结区域）
  List<Widget> _buildHeader(
    BuildContext context,
    BuildContext tableContext,
    List<AppTableColumn> cols,
    double tableViewportWidth,
    AppTableColumnFrozen frozen,
  ) {
    var myProvider = context.read<AppTableStateManage>();

    //是否支持多选
    var isMult = widget.checkType == TableCheckedEnum.multiple;

    // Icon(Icons.home_mini)

    List<Widget> headerCells = [
      Container(
        width: myProvider.indexColumnWidth,
        height: myProvider.headerHeight,
        decoration: BoxDecoration(color: widget.headerBgColor, border: _getBorderSide(context)),
        child:
            isMult
                ? Selector<
                  AppTableStateManage,
                  ({
                    // CheckedStatus tableCheckStatus,
                    bool? isAllChecked,
                  })
                >(
                  selector:
                      (context, provider) => (
                        // tableCheckStatus: provider.tableCheckStatus,
                        isAllChecked: provider.isAllChecked,
                      ),
                  builder: (context, data, child) {
                    return Container(
                      width: _indexColIconWidth,
                      child: Checkbox(
                        value: data.isAllChecked, //rowData.isChecked,
                        tristate: true,
                        onChanged: (value) {
                          context.read<AppTableStateManage>().setTableChecked();
                        },
                      ),
                    );
                    // return GestureDetector(
                    //   child: Icon(
                    //     data.tableCheckStatus == CheckedStatus.none
                    //         ? Icons.check_box_outline_blank
                    //         : data.tableCheckStatus == CheckedStatus.all
                    //         ? Icons.check_box_outlined
                    //         : Icons.home_mini,
                    //   ),
                    //   onTap: () {
                    //     //全选、全不选
                    //     context.read<AppTableStateManage>().setTableChecked();
                    //   },
                    // );
                  },
                )
                : Center(child: Text('序号')),
      ),
    ];

    List<AppTableColumn> columnsTemp = [];
    if (frozen == AppTableColumnFrozen.start) {
      columnsTemp = cols.where((c) => c.frozen == frozen).toList();
    } else {
      columnsTemp = cols;
    }

    for (var i = 0; i < columnsTemp.length; i++) {
      final column = columnsTemp[i];

      headerCells.add(
        Selector<
          AppTableStateManage,
          ({AppTableColumn item, List<int> seledColumnIndexs, int flag})
        >(
          selector:
              (context, provider) => (
                item: provider.columns[i],
                seledColumnIndexs: provider.selectedColumnIndexs,
                flag: provider.columnsChanged,
              ),
          builder: (context, data, child) {
            return Stack(
              clipBehavior: Clip.none, //关键：允许子组件超出Stack范围
              children: [
                /// 拖动列排序
                MouseRegion(
                  cursor:
                      data.seledColumnIndexs.contains(i)
                          ? SystemMouseCursors.move
                          : SystemMouseCursors.basic,
                  child: GestureDetector(
                    behavior: HitTestBehavior.opaque,
                    onTap: () {
                      /// 模拟选中两列
                      myProvider.setSelectedColumn([i]);
                    },
                    onHorizontalDragStart: (details) {
                      final tableBounds = _getTableBounds(tableContext)!;

                      /// 拖拽选中的列
                      myProvider.columnDragStart(tableBounds.position, details, i, tableContext);
                    },
                    onHorizontalDragUpdate: (details) {
                      final tableBounds = _getTableBounds(tableContext)!;

                      myProvider.columnDragUpdate(
                        context,
                        tableBounds.position,
                        tableBounds.size,
                        details,
                        i,
                      );
                    },
                    onHorizontalDragEnd: (details) {
                      myProvider.columnDragEnd();
                    },
                    child: Container(
                      // key:
                      //     frozen == AppTableColumnFrozen.none
                      //         ? myProvider.columnsKeys[i]
                      //         : null,
                      width: column.width,
                      height: myProvider.rowHeight,
                      padding: myProvider.headerCellPadding,
                      decoration: BoxDecoration(
                        color:
                            data.seledColumnIndexs.contains(i)
                                ? myProvider.selectedColumnColor
                                : widget.headerBgColor,
                        border: _getBorderSide(context),
                      ),
                      alignment: column.alignment ?? Alignment.centerLeft,

                      /// 拖拽列标题排序
                      child: Row(
                        children: [
                          Container(
                            child:
                                column.headerBuilder != null
                                    ? column.headerBuilder!(context)
                                    : Text('${column.text}'),
                          ),

                          Spacer(),

                          if (column.showMore)
                            // 列下拉更多操作按钮
                            AppDropdown(
                              placement: DropdownPlacement.bottomRight,
                              size: DropdownSize.small,
                              items: [
                                DropdownItem(text: '编辑', value: 'edit', iconData: Icons.edit),
                                DropdownItem(
                                  text: '删除',
                                  value: 'delete',
                                  iconData: Icons.delete,
                                  divided: true,
                                ),
                                DropdownItem(
                                  text: '测试111',
                                  value: 3,
                                  disabled: true,
                                  iconData: IconFont.mianxing_huibao,
                                ),
                              ],
                              text: '选择选项',
                              trigger: DropdownTrigger.click, // 可选hover或click
                              child: AppButton(
                                iconData: Icons.more_vert,
                                size: ButtonSize.medium,
                                color: context.icon300,
                                type: ButtonType.transparent,
                                onPressed: () {},
                              ),
                              onItemSelected: (item) {
                                if (item.value == 'edit') {
                                  _appTableStateManage.showEditColumn(column.field);
                                  WidgetsBinding.instance.addPostFrameCallback((_) {
                                    _showAddColumnDialog(context, tableContext, columnIndex: i);
                                  });
                                } else if (item.value == 'delete') {
                                  _appTableStateManage.removeColumn(column.field);
                                }
                              },
                            ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            );
          },
        ),
      );
    }

    ///添加列按钮（列头部分）
    if (widget.showAddColumnButton && frozen == AppTableColumnFrozen.none) {
      headerCells.add(
        _addColumnArea(
          context,
          tableContext,
          tableViewportWidth: tableViewportWidth,
          child: GestureDetector(
            onTap: () {
              _showAddColumnDialog(context, tableContext);
              // _addColumnTooltipController.toggleTooltip();
            },
            child: Center(child: Icon(Icons.add)),
          ),
          // child: AppCustomTooltip(
          //   showArrow: false,
          //   manual: true,
          //   controller: _addColumnTooltipController,
          //   // isShow: isShowOutsideClickTooltip1,
          //   closeOnOutsideClick: true,
          //   onVisibleChange: (visible) {
          //     // print('时间: ${DateTime.now()}');
          //     setVisiableTime = DateTime.now();
          //     addColumnTooltipVisiable = visible;
          //   },
          //   // content: '点击外部区域会自动关闭此提示框',
          //   contentChild: ShadowBox(
          //     child: ChangeNotifierProvider.value(
          //       value: _appTableStateManage,
          //       child: Builder(
          //         builder:
          //             (context) => LayoutBuilder(
          //               builder: (ctx, constraints) {
          //                 print('重新加载 添加 列 组件________________');
          //                 return AddColumnContent(
          //                   // columnModel: value,
          //                   width: addColumnContentWidth,
          //                   onClose: () {
          //                     print('onClose');
          //                     _addColumnTooltipController.hideTooltip();
          //                   },
          //                   onSave: (column) {
          //                     print('onSave');
          //                     _addColumnTooltipController.hideTooltip();
          //                     myProvider.saveColumn(column);
          //                   },
          //                 );
          //                 // return Selector<AppTableStateManage, AppTableColumn?>(
          //                 //   selector:
          //                 //       (context, provider) => provider.columnModel,
          //                 //   builder: (context, value, child) {
          //                 //     // print('111111---${value?.field}');
          //                 //     return ;
          //                 //   },
          //                 // );
          //               },
          //             ),
          //       ),
          //     ),
          //   ),
          //   placement: TooltipPlacement.bottomCenter,
          //   child: GestureDetector(
          //     onTap: () {
          //       _showAddColumnDialog(context, tableContext);
          //       // _addColumnTooltipController.toggleTooltip();
          //     },
          //     child: Center(child: Icon(Icons.add)),
          //   ),
          // ),
        ),
      );
    }

    return headerCells;
  }

  /// 打开添加、编辑列弹框
  /// [context] 默认整体的 BuildContext
  /// [tableContext] 表格的 BuildContext；
  /// [columnIndex] columnIndex != null 表示编辑列；columnIndex == null 表示添加；
  void _showAddColumnDialog(BuildContext context, BuildContext tableContext, {int? columnIndex}) {
    //弹框逻辑
    final overlay = Overlay.of(context);
    // final renderBox = context.findRenderObject() as RenderBox;
    // renderBox.localToGlobal(Offset.zero);
    // var tapPosition = _calculateOverlayPosition(context, tableContext, columnIndex);

    final RenderBox overlayBox = overlay.context.findRenderObject() as RenderBox;

    OverlayEntry? entry;

    entry = OverlayEntry(
      builder:
          (innerContext) => LayoutBuilder(
            builder: (ctx, constraints) {
              var tapPosition = _calculateOverlayPosition(context, tableContext, columnIndex);

              // print('tapPosition.dy====${tapPosition.dy}');

              // // 转换为相对于 Overlay 的坐标
              // final RenderBox tableBox = tableContext.findRenderObject() as RenderBox;
              // final tableLocal = tableBox.localToGlobal(Offset.zero, ancestor: overlayBox);

              final adjustedPosition = Offset(
                tapPosition.dx - overlayBox.localToGlobal(Offset.zero).dx,
                tapPosition.dy - overlayBox.localToGlobal(Offset.zero).dy,
              );

              return GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () {
                  print('关闭');
                  entry?.remove();
                },
                child: Stack(
                  children: [
                    Positioned(
                      left: adjustedPosition.dx,
                      top: adjustedPosition.dy,
                      child: GestureDetector(
                        onTap: () {
                          print('点击---弹框组件----不关闭');
                        },
                        child: Material(
                          elevation: 8,
                          child: ConstrainedBox(
                            constraints: BoxConstraints(
                              maxWidth: MediaQuery.of(context).size.width * 0.7,
                            ),
                            child: ShadowBox(
                              child: ChangeNotifierProvider.value(
                                value: _appTableStateManage,
                                child: AddColumnContent(
                                  // columnModel: value,
                                  width: addColumnContentWidth,
                                  onClose: () {
                                    entry?.remove();
                                  },
                                  onSave: (column) {
                                    var myProvider = context.read<AppTableStateManage>();
                                    myProvider.saveColumn(column);
                                    entry?.remove();
                                  },
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
    );

    overlay.insert(entry);
  }

  /// 计算新增、编辑列弹框位置
  Offset _calculateOverlayPosition(
    BuildContext context,
    BuildContext tableContext,
    int? columnIndex,
  ) {
    var myProvider = context.read<AppTableStateManage>();
    //表格位置信息
    final tablePosition = _getTableBounds(tableContext)!.position;
    //所有列对应的分割线
    var lines = myProvider.globalPositionsOfColumns(context, tablePosition);

    //如果是编辑（columnIndex != null），那么索引+1等于列的有边框坐标；如果为添加：那么就是lines最后一项 + 添加按钮宽度；
    var colIdx = columnIndex != null ? columnIndex + 1 : lines.length - 1;

    //如果是冻结列，需要考虑x轴滚动位置；非冻结列不需要（lines就是真实位置）
    var h_s = myProvider.horizontalScrollController;
    var temp = h_s.hasClients ? h_s.offset : 0;
    var isFrozenWidth =
        columnIndex != null && myProvider.columns[columnIndex].frozen == AppTableColumnFrozen.start
            ? temp
            : 0;

    // 计算弹框 x 坐标
    var tempX =
        lines[colIdx] +
        (columnIndex == null ? myProvider.headerHeight : 0) -
        addColumnContentWidth +
        isFrozenWidth;

    // 计算弹框 y 坐标
    var tempY = tablePosition.dy + myProvider.headerHeight + 10;

    // 最终算出来的弹框坐标
    var tapPosition = Offset(tempX, tempY);

    return tapPosition;
  }

  /// 拖拽改变列宽的小组件
  /// [context] 默认整体的 BuildContext
  /// [tableContext] 表格的 BuildContext
  List<Widget> _buildHeaderResize(
    BuildContext context,
    BuildContext tableContext,
    AppTableColumnFrozen frozen,
  ) {
    var myProvider = context.read<AppTableStateManage>();

    if (myProvider.columns.isEmpty) {
      return [const SizedBox.shrink()];
    }
    List<Widget> result = [];

    //所有列
    var stateMgmt = context.select<
      AppTableStateManage,
      ({List<AppTableColumn> cols, int columnsChanged, int horizontalScrollFlag})
    >(
      (provider) => (
        cols: provider.columns,
        columnsChanged: provider.columnsChanged,
        horizontalScrollFlag: provider.horizontalScrollFlag,
      ),
    );

    var cols = stateMgmt.cols;

    //需要渲染的列
    var columnsTemp = [];
    if (frozen == AppTableColumnFrozen.start) {
      columnsTemp = cols.where((c) => c.frozen == frozen).toList();
    } else {
      columnsTemp = cols;
    }

    //索引列宽度
    double widthFlag = myProvider.indexColumnWidth;

    for (var i = 0; i < columnsTemp.length; i++) {
      int index = i;
      // int indexOfAllColumn = i;
      AppTableColumn currColumn = columnsTemp[index];

      widthFlag += currColumn.width;

      var h_scroll = myProvider.horizontalScrollController;
      var h_scroll_offset = h_scroll.hasClients ? h_scroll.offset : 0;

      //占位
      Widget widget = SizedBox.shrink();
      if (frozen == AppTableColumnFrozen.start ||
          (frozen == AppTableColumnFrozen.none && currColumn.frozen == AppTableColumnFrozen.none)) {
        widget = Positioned(
          left:
              widthFlag -
              (myProvider.dragBlockWidth / 2).floor() -
              (currColumn.frozen == AppTableColumnFrozen.none ? h_scroll_offset : 0),
          child: MouseRegion(
            cursor: SystemMouseCursors.resizeColumn,
            child: GestureDetector(
              behavior: HitTestBehavior.opaque,
              onHorizontalDragStart: (details) {
                final tableBounds = _getTableBounds(tableContext)!;

                myProvider.resizeDragStart(index, tableBounds.position, details);
              },
              onHorizontalDragUpdate: (details) {
                final tableBounds = _getTableBounds(tableContext)!;

                myProvider.resizeDragUpdate(tableBounds.position, details, currColumn);
              },
              onHorizontalDragEnd: (details) {
                myProvider.resizeDragEnd(currColumn, index);

                /// 修正场景：
                /// 当x轴滚动条滚动到最右侧时（滚动到最大位置）,并且拖拽列宽向左（改小列宽），导致列头和表格行错位；
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  if (_horizontalScrollController.hasClients) {
                    final maxScroll = _horizontalScrollController.position.maxScrollExtent;

                    if (_horizontalScrollController.offset >= maxScroll) {
                      _horizontalScrollController.jumpTo(maxScroll - 1);
                    }
                  }
                });
              },

              /// 左右拖拽改变列宽
              child: Container(
                width: myProvider.dragBlockWidth,
                height: myProvider.headerHeight,
                color: myProvider.dragBlockColor,
              ),
            ),
          ),
        );
      }

      result.add(widget);
    }

    return result;
  }
  // // 新增内部方法处理树形单元格渲染
  // Widget _buildTreeCell(
  //   BuildContext context,
  //   AppTableRowData rowData,
  //   String field,
  // ) {
  //   return Row(
  //     children: [
  //       // 缩进
  //       // SizedBox(width: rowData.level * widget.indentSize),
  //       // Container(color: Colors.red, width: rowData.level * widget.indentSize),

  //       // 展开/收起图标
  //       // if (rowData.hasChildren)
  //       //   IconButton(
  //       //     icon: Icon(
  //       //       rowData.isExpanded ? widget.expandedIcon : widget.collapsedIcon,
  //       //       size: widget.treeIconSize,
  //       //     ),
  //       //     onPressed: () {
  //       //       context.read<AppTableStateManage>().toggleNode(rowData.id);
  //       //     },
  //       //     padding: EdgeInsets.zero,
  //       //     constraints: BoxConstraints.tightFor(
  //       //       width: widget.treeIconSize,
  //       //       height: widget.treeIconSize,
  //       //     ),
  //       //   ),

  //       // 单元格内容
  //       Expanded(child: Text(rowData.data[field] ?? '')),
  //     ],
  //   );
  // }

  /// 设置边界，保证取值在所有列的分界线之内（第一列的开始边界，最后一列的结束边界）
  double _setLinesSide(BuildContext context, Offset? tablePosition, double x, int offset) {
    var result = x;

    var myProvider = context.read<AppTableStateManage>();

    var lines = myProvider
        .globalPositionsOfColumns(context, tablePosition)
        .map((c) => c - tablePosition!.dx);

    var firstLine = lines.first;

    var lastLine = lines.last;

    if (result < firstLine - offset) {
      result = firstLine - offset;
    } else if (result > lastLine - offset) {
      result = lastLine - offset;
    }

    return result;
  }

  Widget _buildRowCellWrapper(
    BuildContext context,
    AppTableStateManage provider,
    int rowIdx,
    Widget child,
  ) {
    var myProvider = provider;
    return MouseRegion(
      onHover: (event) {
        myProvider.handleHoverRow(rowIdx);
      },
      onExit: (event) {
        // 拖拽行排序时不能清空hoverId
        if (myProvider.startRowGlobalPosition == null) {
          myProvider.handleHoverRow(null);
        }
      },
      child: child,
    );
  }

  /// 构建表格行
  /// [context] 默认整体的 BuildContext
  /// [tableContext] 表格的 BuildContext
  /// [idx] 当前行的索引
  /// [frozen] 是否为冻结区域（表格由两部分组成，左侧冻结区域、右侧非冻结区域）
  /// [tableViewportWidth] 表格可视窗口宽度
  /// [tableViewportHeight] 表格可视窗口高度
  ///
  List<Widget> _buildRow(
    BuildContext context,
    BuildContext tableContext,
    int idx,
    AppTableColumnFrozen frozen,
    double tableViewportWidth,
    double tableViewportHeight,
  ) {
    var myProvider = context.read<AppTableStateManage>();
    if (idx < myProvider.rows.length) {
      Map<String, dynamic> row = myProvider.rows[idx];

      var rowId = row[_rowKey].toString();

      var rowIndex = idx;
      var rowData = myProvider.getRowData(rowIndex); // 获取对应的 AppTableRowData

      var nextRowIndex = idx + 1;
      var nextRowData =
          nextRowIndex < myProvider.rows.length - 1 ? myProvider.getRowData(nextRowIndex) : null;

      var nextRowLevel = nextRowData != null ? nextRowData.level - 1 : 0;

      bool isLastInGroup = myProvider.isLastVisibleInGroup(rowIndex);

      List<Widget> cells = [];

      //索引列默认固定在左侧冻结列区域
      if (frozen == AppTableColumnFrozen.start) {
        /// 添加索引列
        cells.add(
          _buildRowCellWrapper(
            context,
            myProvider,
            rowIndex,

            Selector<AppTableStateManage, ({int? hoverRowIdx})>(
              selector: (context, provider) => (hoverRowIdx: provider.hoverRowIdx),

              builder: (context, data, child) {
                return Container(
                  width: myProvider.indexColumnWidth,
                  decoration: BoxDecoration(
                    color: data.hoverRowIdx == rowIndex ? myProvider.rowHoverBgColor : null,
                    border: _getBorderSide(
                      context,
                      bottomColor: isLastInGroup ? myProvider.borderColor : Colors.transparent,
                    ),
                  ),
                  child: Stack(
                    children: [
                      widget.indexCellBuilder != null
                          ? Center(child: widget.indexCellBuilder!(context, idx))
                          : Center(
                            child:
                                data.hoverRowIdx == rowIndex
                                    ? Text('')
                                    : Text(
                                      _isTree
                                          ? '${rowData.levelOneIndex != null ? (rowData.levelOneIndex! + 1) : ''}'
                                          : '${idx + 1}',
                                    ),
                          ),

                      if (data.hoverRowIdx == rowIndex)
                        Positioned(
                          child: Center(
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                              children: [
                                /// 拖拽行排序
                                MouseRegion(
                                  cursor: SystemMouseCursors.move, //.resizeUpDown,
                                  child: GestureDetector(
                                    behavior: HitTestBehavior.opaque,

                                    /// 拖拽行上下排序
                                    child: Icon(Icons.drag_indicator, size: _indexColIconWidth),
                                  ),
                                ),

                                Selector<
                                  AppTableStateManage,
                                  ({
                                    int rowChange,
                                    // List<AppTableRowData> selectRows,
                                  })
                                >(
                                  selector:
                                      (context, provider) => (
                                        rowChange: provider.rowsChanged,
                                        // selectRows: provider.selectRows,
                                      ),
                                  builder: (context, data, child) {
                                    return widget.checkType == TableCheckedEnum.multiple
                                        ? Container(
                                          width: _indexColIconWidth,
                                          child: Checkbox(
                                            value: rowData.isChecked,
                                            onChanged: (value) {
                                              context.read<AppTableStateManage>().setCheckedRow(
                                                rowData,
                                              );
                                            },
                                          ),
                                        )
                                        // Icon(
                                        //   size: _indexColIconWidth,
                                        //   rowData.isChecked
                                        //       ? Icons.check_box_outlined
                                        //       : Icons.check_box_outline_blank,
                                        // )
                                        : widget.checkType == TableCheckedEnum.single
                                        ? GestureDetector(
                                          onTap: () {
                                            context.read<AppTableStateManage>().setCheckedRow(
                                              rowData,
                                            );
                                          },
                                          child: Icon(
                                            size: _indexColIconWidth,
                                            rowData.isChecked
                                                ? Icons.radio_button_on
                                                : Icons.radio_button_unchecked,
                                          ),
                                        )
                                        : const SizedBox.shrink();
                                  },
                                ),

                                GestureDetector(
                                  child: Icon(Icons.open_in_full, size: _indexColIconWidth),
                                  onTap: () {
                                    print('最大化编辑');
                                  },
                                ),
                              ],
                            ),
                          ),
                        ),
                    ],
                  ),
                );
              },
            ),
          ),
        );
      }

      var columnsTemp = myProvider.columns.where((c) => c.frozen == frozen).toList();

      for (var i = 0; i < columnsTemp.length; i++) {
        final colIdx = i;
        //针对冻结区域和非冻结区域的索引（假设冻结区域有两列，那么非冻结区域第一列的索引就是2）
        final realColIdx =
            i + (frozen == AppTableColumnFrozen.start ? 0 : myProvider.columnsFrozen.length);
        final column = columnsTemp[colIdx];
        cells.add(
          _buildRowCellWrapper(
            context,
            myProvider,
            rowIndex,

            Selector<AppTableStateManage, ({List<int> seledColumnIndexs, int? hoverRowIdx})>(
              selector:
                  (context, provider) => (
                    seledColumnIndexs: provider.selectedColumnIndexs,
                    hoverRowIdx: provider.hoverRowIdx,
                  ),
              builder: (context, data, child) {
                return Row(
                  children: [
                    // 缩进部分（包含折叠按钮）
                    if (colIdx == 0 && _isTree && frozen == AppTableColumnFrozen.start)
                      Container(
                        decoration: BoxDecoration(
                          color:
                              data.seledColumnIndexs.contains(realColIdx) ||
                                      data.hoverRowIdx == rowIndex
                                  ? myProvider.selectedColumnColor
                                  : null,
                          border: _getBorderSide(
                            // 树形表格最后一级需要补充底部边框
                            context,
                            renderRightBorder: false,
                            bottomColor:
                                isLastInGroup ? myProvider.borderColor : Colors.transparent,
                          ),
                        ),
                        child: Stack(
                          clipBehavior: Clip.none, //关键：允许子组件超出Stack范围
                          children: [
                            // 树结构连接线（竖）
                            for (int i = 0; i < rowData.level; i++)
                              Positioned(
                                top: -(widget.rowHeight / 2),
                                left: i * widget.indentSize + (widget.indentSize / 2),
                                child: Container(
                                  width: 1,
                                  color: widget.joinLineColor,
                                  height: widget.rowHeight,
                                ),
                              ),

                            // 树结构连接线（每一级的连接线底部样式）
                            if (_isTree)
                              // rowData.level - (nextRowData != null ? nextRowData.level - 1 : 0)
                              Positioned(
                                bottom: -1,
                                left: (nextRowLevel + 1) * widget.indentSize,
                                child: Container(
                                  width: (rowData.level - nextRowLevel) * widget.indentSize,
                                  height: 1,
                                  color: Colors.pink,
                                ),
                              ),

                            // for (
                            //   int i = 1;
                            //   i <= rowData.level - nextRowLevel;
                            //   i++
                            // )
                            //   Positioned(
                            //     bottom: -1,
                            //     left:
                            //         i * widget.indentSize +
                            //         nextRowLevel * widget.indentSize,
                            //     child: Container(
                            //       width: 11, //widget.indentSize,
                            //       height: 1,
                            //       color: Colors.red,
                            //     ),
                            //   ),
                            Row(
                              children: [
                                SizedBox(
                                  width: rowData.level * widget.indentSize,
                                  height: myProvider.rowHeight,
                                ),

                                rowData.hasChildren
                                    ? IconButton(
                                      icon: Icon(
                                        rowData.isExpanded
                                            ? widget.expandedIcon
                                            : widget.collapsedIcon,
                                        size: widget.treeIconSize,
                                      ),
                                      onPressed: () {
                                        print('折叠节点：${rowData.id}---i===${i}');
                                        context.read<AppTableStateManage>().toggleNode(rowData.id);
                                      },
                                      padding: EdgeInsets.zero,
                                      constraints: BoxConstraints.tightFor(
                                        width: widget.treeIconSize,
                                        height: widget.treeIconSize,
                                      ),
                                    )
                                    : SizedBox(width: widget.treeIconSize),
                              ],
                            ),
                          ],
                        ),
                      ),
                    GestureDetector(
                      behavior: HitTestBehavior.opaque,
                      onTap: () {
                        myProvider.setSelectedColumn(null);
                        myProvider.setEditCell();
                        if (widget.onCellTap != null) {
                          widget.onCellTap!(context, idx, colIdx, row[column.field]);
                        }
                      },
                      onDoubleTap: () {
                        if (column.cellBuilder != null) {
                          print('自定义列展示不支持编辑');
                        }

                        myProvider.setEditCell(rowId: rowId, columnId: column.field);
                        print('rowId==${rowId} columnId==${column.field}');
                      },
                      child: Container(
                        width:
                            colIdx == 0 && _isTree && frozen == AppTableColumnFrozen.start
                                ? column.width -
                                    rowData.level * widget.indentSize -
                                    widget.treeIconSize
                                : column.width,
                        height: myProvider.rowHeight,
                        padding: myProvider.cellPadding,
                        alignment: column.alignment ?? Alignment.centerLeft,
                        decoration: BoxDecoration(
                          color:
                              data.seledColumnIndexs.contains(realColIdx) ||
                                      data.hoverRowIdx == rowIndex
                                  ? myProvider.selectedColumnColor
                                  : null,
                          border: _getBorderSide(context),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            // column.cellBuilder != null
                            //     ? column.cellBuilder!(context, row[column.field])
                            //     : Text('${row[column.field] ?? ""}'),

                            // colIdx == 0
                            // ? Expanded(
                            //   // child: Text(rowData.data[column.field] ?? ''),
                            //   child: AppTableCell(
                            //     column: column,
                            //     rowData: row,
                            //     rowIdx: rowIndex,
                            //     columnIdx: colIdx,
                            //     rowId: rowId, //
                            //     columnId: column.field,
                            //   ),
                            // ) // 使用树形构建器
                            // :
                            column.cellBuilder?.call(context, row[column.field], column, row) ??
                                Expanded(
                                  child: AppTableCell(
                                    column: column,
                                    rowData: row,
                                    rowIdx: rowIndex,
                                    columnIdx: colIdx,
                                    rowId: rowId, //
                                    columnId: column.field,
                                  ),
                                ),
                            // Text(
                            //   '${row[column.field] ?? ""}-${column.type}',
                            // )

                            // // 测试
                            // if (colIdx == 0 &&
                            //     row[column.field].toString().isEmpty &&
                            //     !_isTree)
                            //   Text(
                            //     '${row[widget.uniqueId].toString().substring(0, row[widget.uniqueId].length > 13 ? 13 : row[widget.uniqueId].length)}',
                            //   ),
                            // if (colIdx == 0 &&
                            //     row[column.field].toString().isEmpty &&
                            //     _isTree)
                            //   Text(
                            //     '${row[_rowKey].toString().substring(0, row[_rowKey].length > 13 ? 13 : row[_rowKey].length)}',
                            //   ),

                            // /// 如果是树结构，并且是第一列，显示子节点统计数据
                            // if (_isTree &&
                            //     frozen == AppTableColumnFrozen.start &&
                            //     colIdx == 0)
                            //   Text(
                            //     '${rowData.childrenCount.toString()}-${rowData.descendantsCount.toString()}-${rowData.isLeaf ? "是" : "否"}-${rowData.level - (nextRowData != null ? nextRowData.level - 1 : 0)}-${rowData.level}',
                            //     style: TextStyle(color: Colors.red),
                            //   ),

                            /// 如果是树结构，并且是第一列，显示子节点统计数据
                            /// 添加子节点
                            if (_isTree && frozen == AppTableColumnFrozen.start && colIdx == 0)
                              Container(
                                height: 24,
                                width: 24,
                                margin: EdgeInsets.symmetric(horizontal: 5),
                                child: Center(
                                  child: Material(
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(4),
                                      side: BorderSide.none,
                                    ),
                                    color: const Color.fromARGB(255, 242, 225, 224),
                                    child: Tooltip(
                                      message: '添加子节点',
                                      child: InkWell(
                                        onTap: () {
                                          myProvider.addRow(
                                            parentId: rowData.data[widget.treeConfig!.idField],
                                          );
                                        },
                                        child: Icon(Icons.add, size: 18),
                                      ),
                                    ),
                                  ),
                                ),
                              ),

                            ///子节点数量
                            if (_isTree && frozen == AppTableColumnFrozen.start && colIdx == 0)
                              Text(rowData.childrenCount.toString()),
                          ],
                        ),
                      ),
                    ),
                    // 内容部分
                  ],
                );
              },
            ),
          ),
        );
      }

      if (frozen == AppTableColumnFrozen.none && widget.showAddColumnButton) {
        //添加列按钮（空白行对应区域）
        cells.add(
          //如果是最后一行，且表格不显示添加行按钮，需要显示底边框
          _addColumnArea(
            context,
            tableContext,
            tableViewportWidth: tableViewportWidth,
            renderBottomBorder: idx == myProvider.rows.length - 1 && !widget.showAddRowButton,
          ),
        );
      }

      return cells;
    } else if (widget.showAddRowButton && idx == myProvider.rows.length) {
      //空白行上面的添加行按钮
      return [
        //添加行按钮区域
        _addRowArea(context, frozen, tableViewportHeight: tableViewportHeight),

        if (frozen == AppTableColumnFrozen.none && widget.showAddColumnButton)
          //添加列按钮区域（添加行对应区域）
          _addColumnArea(context, tableContext, tableViewportWidth: tableViewportWidth),
      ];
    } else {
      //底部空白行，用于增加空白区域
      return [SizedBox(height: myProvider.rowHeight)];
    }
  }

  /// 添加列按钮区域
  Widget _addRowArea(
    BuildContext context,
    AppTableColumnFrozen frozen, {
    double tableViewportHeight = 0,
  }) {
    var myProvider = context.read<AppTableStateManage>();
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      onHover: (event) {
        myProvider.setHoverAddRowButton(true);
      },
      onExit: (event) {
        myProvider.setHoverAddRowButton(false);
      },

      child: Selector<AppTableStateManage, bool>(
        selector: (context, provider) => provider.hoverAddRowButton,
        builder: (context, isHover, child) {
          return GestureDetector(
            onTap: () {
              myProvider.addRow(parentId: null);
            },
            child: Container(
              color: isHover ? widget.hoverRowColor : Colors.transparent,
              child: Row(
                children: [
                  // 添加行按钮（索引列中的按钮）
                  if (frozen == AppTableColumnFrozen.start)
                    Container(
                      height: widget.rowHeight,
                      width: myProvider.indexColumnWidth,
                      decoration: BoxDecoration(border: _getBorderSide(context)),
                      child: Center(child: Icon(Icons.add)),
                    ),
                  //添加行按钮（空白区域）
                  Container(
                    height: myProvider.rowHeight,
                    width:
                        frozen == AppTableColumnFrozen.start
                            ? myProvider.tableWidthOfFrozenArea - myProvider.indexColumnWidth
                            : myProvider.tableContentWidth -
                                myProvider.tableWidthOfFrozenArea -
                                (widget.showAddColumnButton ? myProvider.headerHeight : 0),
                    decoration: BoxDecoration(
                      border: _getBorderSide(
                        context,
                        renderRightBorder: frozen != AppTableColumnFrozen.start,
                      ),
                    ),
                    child: const SizedBox.shrink(),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  /// 添加列按钮
  /// 添加列由三部分组成，列头部分，表格行对应部分，表格底部添加行按钮对应部分
  Widget _addColumnArea(
    BuildContext context,
    BuildContext tableContext, {
    double tableViewportWidth = 0,
    bool renderBottomBorder = true,
    Widget? child,
  }) {
    var myProvider = context.read<AppTableStateManage>();

    return MouseRegion(
      cursor: SystemMouseCursors.click,
      onEnter: (event) {
        myProvider.setHoverAddColumnButton(true);
      },
      onExit: (event) {
        myProvider.setHoverAddColumnButton(false);
      },
      child: Selector<AppTableStateManage, bool>(
        selector: (context, provider) => provider.hoverAddColumnButton,
        builder: (context, isHover, _child) {
          return GestureDetector(
            onTap: () {
              // if (widget.onAddColumn != null) {
              //   var newCol = AppTableGeneralHelper.newColumn(tableViewportWidth);
              //   // 调用回调函数，传入当前上下文和列索引
              //   widget.onAddColumn!(
              //     context,
              //     newCol,
              //     _appTableStateManage,
              //     tableViewportWidth,
              //   );
              // }

              /// 因为表格最右侧添加列按钮是由多个组件组合起来的
              /// 避免，当添加列头的加号，然后再点击加号下面延申到每一行的添加按钮时，添加弹框会先隐藏，然后再显示，不符合toggle的效果，所以添加此逻辑代码
              // if (setVisiableTime != null) {
              //   var now = DateTime.now();
              //   if (now.difference(setVisiableTime!).inMilliseconds < 200) {
              //     return;
              //   }
              // }

              //添加列
              _appTableStateManage.showAddColumn();
              WidgetsBinding.instance.addPostFrameCallback((_) {
                // _addColumnTooltipController.showTooltip();
                _showAddColumnDialog(context, tableContext);
              });
            },
            child: Container(
              width: myProvider.headerHeight,
              height: myProvider.headerHeight,
              decoration: BoxDecoration(
                color: isHover ? widget.hoverRowColor : Colors.transparent,
                border: _getBorderSide(context, renderBottomBorder: renderBottomBorder),
              ),
              child: child ?? SizedBox.shrink(),
            ),
          );
        },
      ),
    );
  }

  /// 统一设置边框样式
  /// [renderRightBorder] 是否需要显示右边框
  /// [renderBottomBorder] 是否需要显示底部边框
  /// [bottomColor] 底部边框颜色
  Border _getBorderSide(
    BuildContext context, {
    bool renderRightBorder = true,
    bool renderBottomBorder = true,
    Color? bottomColor,
  }) {
    var myProvider = context.read<AppTableStateManage>();
    return Border(
      bottom:
          renderBottomBorder
              ? BorderSide(
                color: bottomColor ?? myProvider.borderColor,
                width: myProvider.borderWidth,
              )
              : BorderSide.none,
      right:
          renderRightBorder
              ? BorderSide(color: myProvider.borderColor, width: myProvider.borderWidth)
              : BorderSide.none,
    );
  }

  /// 初始化状态管理类
  _initStateManager() {
    _appTableStateManage = AppTableStateManage(
      // columns: widget.columns,
      // rows: widget.rows,
      checkType: widget.checkType,
      horizontalScrollController: _horizontalScrollController,
      verticalScrollController: _verticalScrollController,
      tableBgColor: widget.tableBgColor,
      borderColor: widget.borderColor,
      joinLineColor: widget.joinLineColor,
      rowHeight: widget.rowHeight,
      indentSize: widget.indentSize,
      borderWidth: widget.borderWidth,
      indexColumnWidth: widget.indexColumnWidth,
      tableWrapperPadding: widget.tableWrapperPadding,
      emptyRowTotal: widget.emptyRowTotal,
      listviewPddingBottom: widget.listviewPddingBottom,
      maxLevel: widget.maxLevel,
      headerCellPadding:
          widget.headerCellPadding ?? const EdgeInsets.symmetric(horizontal: 4, vertical: 0),
      cellPadding: widget.cellPadding ?? const EdgeInsets.symmetric(horizontal: 4, vertical: 0),
      defaultExpandAll: widget.defaultExpandAll,
      showAddRowButton: widget.showAddRowButton,
      showAddColumnButton: widget.showAddColumnButton,
      treeConfig: widget.treeConfig,
      uniqueId: widget.uniqueId,
      frozenAreaMaxWidth: widget.frozenAreaMaxWidth,
    );
  }

  /// 获取表格的位置信息和尺寸
  ({Offset position, Size size})? _getTableBounds(BuildContext context) {
    final RenderBox? tableBox = context.findRenderObject() as RenderBox?;
    if (tableBox == null) return null;

    final position = tableBox.localToGlobal(Offset.zero);
    final size = tableBox.size;

    return (position: position, size: size);
  }

  // // 在 _AppTableState 类中添加获取列位置的方法
  // double _getColumnPosition(BuildContext context, int insertIdx) {
  //   var myProvider = context.read<AppTableStateManage>();
  //   double position = myProvider.indexColumnWidth; // 从序号列宽度开始计算

  //   // 计算到插入位置之前所有列的宽度总和
  //   for (var i = 0; i < insertIdx; i++) {
  //     position += myProvider.columns[i].width;
  //   }

  //   return position;
  // }

  void _initOnLoaded() {
    if (widget.onLoaded == null) return;

    WidgetsBinding.instance.addPostFrameCallback((_) {
      widget.onLoaded!(_appTableStateManage);
    });
  }

  @override
  void dispose() {
    // 移除监听
    _horizontalScrollController.removeListener(_onHorizontalScroll);
    _horizontalScrollController.dispose();
    _verticalScrollController.dispose();
    _verticalScrollControllerShadow.dispose();
    _verticalScrollControllerShadow2.dispose();
    _verticalScrollControllerFrozen.dispose();
    _appTableStateManage.dispose();
    // _addColumnTooltipController.dispose();
    super.dispose();
  }
}

// 主要用来处理当用户按住shift+滚轮使表格左右滚动；
// （当按住shift+滚轮左右滚动表格时，使用的是自带的滚动方式，并不是 handleScroll 方法里面的 jumpTo() 来滚动的。）
// 但是需要通过 handleScroll 方法来告知滚动的方向也距离，方便同步必要的变量；
class AppTableScrollBehavior extends MaterialScrollBehavior {
  @override
  Set<PointerDeviceKind> get dragDevices => {
    PointerDeviceKind.touch,
    // PointerDeviceKind.mouse,
  };

  @override
  Widget buildScrollbar(BuildContext context, Widget child, ScrollableDetails details) {
    return Listener(
      onPointerSignal: (PointerSignalEvent event) {
        if (event is PointerScrollEvent) {
          // 检查是否按下了 Shift 键
          final bool isShiftPressed = HardwareKeyboard.instance.isShiftPressed;
          if (isShiftPressed) {
            var tabState = context.findAncestorStateOfType<_AppTableState>();

            // 获取水平滚动控制器
            final horizontalScrollController = tabState?._horizontalScrollController;

            final tabPosi = tabState?._getTableBounds(context);

            if (horizontalScrollController != null) {
              // 滚动的距离（大于0，向右滚动；小于0，向左滚动）
              double scrollDelta = event.scrollDelta.dy;
              // 是否向右滚动
              bool isRightScroll = scrollDelta > 0;
              var myProvider = context.read<AppTableStateManage>();

              myProvider.handleScroll(
                context,
                tablePosition: tabPosi?.position,
                isRightScroll: isRightScroll,
                rollingDistance: scrollDelta.abs(),
              );
            }
          }
        }
      },
      child: child,
    );
  }

  @override
  Widget buildOverscrollIndicator(BuildContext context, Widget child, ScrollableDetails details) {
    return child;
  }

  @override
  Widget buildViewportChrome(BuildContext context, Widget child, AxisDirection axisDirection) {
    return child;
  }

  @override
  ScrollPhysics getScrollPhysics(BuildContext context) {
    return const AlwaysScrollableScrollPhysics();
  }

  @override
  TargetPlatform getPlatform(BuildContext context) {
    return TargetPlatform.windows;
  }
}

/// 拖拽行组件由于是通过绝对定位定位在每一行上，
/// 所以当鼠标移上去时，鼠标滚动无法上下滚动（无法处理ListView.builder的盾滚动），
/// 通过该组件包裹，只让滚轮事件“穿透”到ListView.builder上。
class OverlayRowWidget extends StatelessWidget {
  final ScrollController listViewController;
  final Widget child;

  const OverlayRowWidget({super.key, required this.listViewController, required this.child});

  @override
  Widget build(BuildContext context) {
    return Listener(
      onPointerSignal: (event) {
        if (event is PointerScrollEvent) {
          // 这里将滚轮事件转发给ListView
          final newOffset = listViewController.offset + event.scrollDelta.dy;
          final maxScroll = listViewController.position.maxScrollExtent;
          final minScroll = listViewController.position.minScrollExtent;
          listViewController.jumpTo(newOffset.clamp(minScroll, maxScroll));
        }
      },
      child: child, // 交互组件
    );
  }
}
